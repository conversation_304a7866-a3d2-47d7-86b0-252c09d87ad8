export const formatDateTime = (ts: string | number | Date, options?: Intl.DateTimeFormatOptions) => {
  const d = new Date(ts);
  // Sensible defaults: short month, 2-digit day, hours:minutes, 24h where locale applies, with TZ abbrev
  const opts: Intl.DateTimeFormatOptions = {
    year: 'numeric', month: 'short', day: '2-digit', hour: '2-digit', minute: '2-digit',
    timeZoneName: 'short',
    ...options,
  };
  return new Intl.DateTimeFormat(undefined, opts).format(d);
};

export const formatDate = (ts: string | number | Date, options?: Intl.DateTimeFormatOptions) => {
  const d = new Date(ts);
  const opts: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: '2-digit', ...options };
  return new Intl.DateTimeFormat(undefined, opts).format(d);
};

export const formatIso = (ts: string | number | Date) => new Date(ts).toISOString();

export const formatCurrency = (amount?: number | null, currency: string = 'MYR', locale?: string) => {
  if (amount == null) return '';
  try {
    return new Intl.NumberFormat(locale, { style: 'currency', currency }).format(amount);
  } catch {
    // Fallback if environment lacks Intl currency support
    return `RM ${Number(amount).toLocaleString()}`;
  }
};

